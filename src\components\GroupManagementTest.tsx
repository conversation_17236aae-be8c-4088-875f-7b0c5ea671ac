import React, { useState } from 'react';
import { Button } from 'antd';
import GroupManagementModal from './GroupManagementModal';

const GroupManagementTest: React.FC = () => {
  const [visible, setVisible] = useState(false);

  return (
    <div style={{ padding: '20px' }}>
      <h2>分组管理测试页面</h2>
      <Button type="primary" onClick={() => setVisible(true)}>
        打开分组管理
      </Button>
      
      <GroupManagementModal
        visible={visible}
        onCancel={() => setVisible(false)}
      />
      
      <div style={{ marginTop: '20px' }}>
        <h3>测试步骤：</h3>
        <ol>
          <li>打开分组管理弹窗</li>
          <li>选择第一页的几个项目，观察"已选择 X 项"的数量</li>
          <li>翻到第二页，再选择几个项目</li>
          <li>观察总数量是否正确累加</li>
          <li>回到第一页，取消选择一些项目</li>
          <li>观察数量是否正确减少</li>
          <li>测试批量删除功能</li>
          <li>测试取消选择功能</li>
        </ol>
      </div>
    </div>
  );
};

export default GroupManagementTest;
