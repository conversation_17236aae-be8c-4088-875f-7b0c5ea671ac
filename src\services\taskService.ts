import type {
  AlertSend,
  DBConnection,
  OtherInfo,
  TaskAlert,
  TaskApiResponse,
  TaskBasic,
  TaskBasicFormData,
  TaskBasicFormDataAdd,
  TaskBasicFormDataUpdateOrDelete,
  TaskDeleteResponse,
  TaskSearchParams,
} from '../types/task';
import { generateAlertSendData, generateDBConnectionData, generateOtherInfoData } from '../utils/generateMockData';
import { parseFrequencyFromString } from '../utils/frequencyConverter';
import { httpClient } from './http/client';


/**
 * 生成模拟告警数据
 */
function generateMockAlerts(count: number): TaskAlert[] {
  const alerts: TaskAlert[] = [];
  const severities = ['low', 'medium', 'high', 'critical'];
  const types = ['isExist', 'isEqual', 'isGreater', 'isLess'];
  const tableNames = ['users', 'orders', 'products', 'logs', 'sessions', 'payments', 'inventory', 'customers', 'transactions', 'reports'];
  const systemNames = ['用户系统', '订单系统', '支付系统', '库存系统', '日志系统', '监控系统', '报表系统', '消息系统', '文件系统', '缓存系统'];
  const checkTypes = ['连接检查', '状态检查', '记录数检查', '性能检查', '空间检查', '权限检查', '配置检查', '服务检查', '网络检查', '安全检查'];

  for (let i = 1; i <= count; i++) {
    const severity = severities[Math.floor(Math.random() * severities.length)];
    const type = types[Math.floor(Math.random() * types.length)];
    const tableName = tableNames[Math.floor(Math.random() * tableNames.length)];
    const systemName = systemNames[Math.floor(Math.random() * systemNames.length)];
    const checkType = checkTypes[Math.floor(Math.random() * checkTypes.length)];

    // 根据类型生成不同的SQL语句
    let sql = '';
    let values: string[] = [];

    switch (type) {
      case 'isExist':
        sql = `SELECT 1 FROM ${tableName} WHERE id = 1`;
        break;
      case 'isEqual':
        sql = `SELECT COUNT(*) FROM ${tableName}`;
        values = [Math.floor(Math.random() * 1000 + 100).toString()];
        break;
      case 'isGreater':
        sql = `SELECT COUNT(*) FROM ${tableName} WHERE status = 'active'`;
        values = [Math.floor(Math.random() * 50 + 10).toString()];
        break;
      case 'isLess':
        sql = `SELECT AVG(response_time) FROM ${tableName}_logs`;
        values = [Math.floor(Math.random() * 500 + 100).toString()];
        break;
    }

    const createTime = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
    const updateTime = new Date(createTime.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);

    alerts.push({
      id: i,
      name: `${systemName}${checkType}_${i.toString().padStart(3, '0')}`,
      severity,
      sql,
      type,
      values,
      create_time: createTime.toISOString().slice(0, 19).replace('T', ' '),
      update_time: updateTime.toISOString().slice(0, 19).replace('T', ' '),
    });
  }

  return alerts;
}

/**
 * 模拟告警数据 - 生成1000条告警数据
 */
export const mockAlertData: TaskAlert[] = generateMockAlerts(1000);

/**
 * 模拟数据库连接数据 - 生成1000条数据库连接数据
 */
export const mockDbConnectionData: DBConnection[] = generateDBConnectionData(1000);

/**
 * 模拟告警发送数据 - 生成1000条告警发送数据
 */
export const mockAlertSendData: AlertSend[] = generateAlertSendData(1000);

/**
 * 模拟其他信息数据 - 生成1000条其他信息数据
 */
export const mockOtherInfoData: OtherInfo[] = generateOtherInfoData(1000);

/**
 * 任务API服务类
 */
export class TaskService {
  /**
   * 获取任务列表
   * @param params 搜索参数
   * @returns 任务列表和总数
   */
  static async getTasks(params: TaskSearchParams): Promise<TaskApiResponse<TaskBasic>> {
    console.log(params);

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 300));

    const response = await httpClient.post<TaskApiResponse<TaskBasicFormData>>('/api/v1/task/exec/select', params);

    console.log(response);

    // 将 TaskBasicFormData 转换为 TaskBasic
    const tableData: TaskBasic[] = response.data.map((item: TaskBasicFormData) => {
      // 解析执行频率字符串为对象格式
      const parsedFrequency = parseFrequencyFromString(item.frequency, false);
      const parsedRetryFrequency = parseFrequencyFromString(item.retry_frequency, true);

      return {
        id: item.id,
        name: item.name,
        group: item.group,
        status: item.status as 'enabled' | 'disabled',
        start_time: item.start_time,
        end_time: item.end_time,
        weekday: item.weekday ? item.weekday.split(',') : [],
        frequency: parsedFrequency || { value: 0, unit: '分' }, // 使用默认值
        retry_num: item.retry_num,
        retry_frequency: parsedRetryFrequency || { value: 0, unit: '分钟' }, // 使用默认值
        alert_task_id: item.alert_task_id ? item.alert_task_id.split(',') : [],
        alert_send_id: item.alert_send_id ? item.alert_send_id.split(',') : [],
        db_connection_id: item.db_connection_id,
        other_info_id: item.other_info_id,
        create_time: item.create_time,
        update_time: item.update_time,
      };
    });

    // 模拟分页和搜索逻辑
    // let filteredData = [...mockTaskData];

    // 根据搜索条件过滤数据
    // if (params.name) {
    //   filteredData = filteredData.filter(item => item.name.includes(params.name!));
    // }

    // if (params.group) {
    //   filteredData = filteredData.filter(item => item.group.includes(params.group!));
    // }

    // if (params.status) {
    //   filteredData = filteredData.filter(item => item.status === params.status);
    // }

    // if (params.weekday) {
    //   filteredData = filteredData.filter(item => item.weekday.includes(params.weekday!));
    // }

    // if (params.frequency) {
    //   filteredData = filteredData.filter(item => item.frequency === params.frequency);
    // }

    // 分页处理
    // const { current = 1, pageSize = 10 } = params;
    // const startIndex = (current - 1) * pageSize;
    // const endIndex = startIndex + pageSize;
    // const paginatedData = filteredData.slice(startIndex, endIndex);

    return {
      data: tableData,
      total: response.total,
      success: true,
    };
  }

  /**
   * 删除单个任务
   * @param id 任务ID
   * @returns 是否删除成功
   */
  static async deleteTask(id: number): Promise<TaskApiResponse<TaskDeleteResponse>> {
    console.log('删除任务:', id);
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    // 请求后端参数包装成map
    const params = {
      ids: [id],
    };

    const res = await httpClient.post<TaskApiResponse<TaskDeleteResponse>>('/api/v1/task/exec/delete/id', params);

    return res;
  }

  /**
   * 批量删除任务
   * @param ids 任务ID数组
   * @returns 是否删除成功
   */
  static async batchDeleteTasks(ids: number[]): Promise<TaskApiResponse<TaskDeleteResponse>> {
    console.log('批量删除任务:', ids);
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 800));

    // 请求后端参数包装成map
    const params = {
      ids: ids,
    };

    const res = await httpClient.post<TaskApiResponse<TaskDeleteResponse>>('/api/v1/task/exec/delete/id', params);

    return res;
  }

  /**
   * 添加任务
   * @param taskData 任务数据
   * @returns 新创建的任务信息
   */
  static async addTask(taskBasicFormDataAdd: TaskBasicFormDataAdd): Promise<TaskBasicFormDataAdd> {
    console.log('添加任务:', taskBasicFormDataAdd);
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 600));

    // 模拟返回新创建的任务
    // const newTask: TaskBasicFormDataAdd = {
    // id: Date.now(),
    // ...taskBasicFormDataAdd,
    // createTime: new Date().toLocaleString(),
    // };

    return taskBasicFormDataAdd;
  }

  /**
   * 更新任务
   * @param id 任务ID
   * @param taskData 更新的任务数据
   * @returns 更新后的任务信息
   */
  static async updateTask(id: number, taskData: Partial<TaskBasicFormDataAdd>): Promise<TaskBasicFormDataAdd> {
    console.log('更新任务:', id, taskData);
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    // 模拟返回更新后的任务
    const existingTask = mockTaskData.find(task => task.id === id);
    if (!existingTask) {
      throw new Error('任务不存在');
    }

    // 将 TaskBasic 转换为 TaskBasicFormDataAdd 格式
    const convertedTask: TaskBasicFormDataAdd = {
      name: existingTask.name,
      group: existingTask.group,
      status: existingTask.status,
      start_time: existingTask.start_time,
      end_time: existingTask.end_time,
      weekday: Array.isArray(existingTask.weekday) ? existingTask.weekday.join(',') : existingTask.weekday,
      frequency: typeof existingTask.frequency === 'object' ? `${existingTask.frequency.value}${existingTask.frequency.unit}` : existingTask.frequency,
      retry_num: existingTask.retry_num,
      retry_frequency: typeof existingTask.retry_frequency === 'object' ? `${existingTask.retry_frequency.value}${existingTask.retry_frequency.unit}` : existingTask.retry_frequency,
      alert_task_id: Array.isArray(existingTask.alert_task_id) ? existingTask.alert_task_id.join(',') : existingTask.alert_task_id,
      alert_send_id: Array.isArray(existingTask.alert_send_id) ? existingTask.alert_send_id.join(',') : existingTask.alert_send_id,
      db_connection_id: existingTask.db_connection_id,
      other_info_id: existingTask.other_info_id,
    };

    return {
      ...convertedTask,
      ...taskData,
    };
  }

  /**
   * 获取告警列表
   */
  static async getAlerts(): Promise<TaskAlert[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return mockAlertData;
  }

  /**
   * 根据ID数组获取告警列表
   */
  static async getAlertsByIds(ids: string[]): Promise<TaskAlert[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    // 模拟根据ID查询
    return mockAlertData.filter(alert => ids.includes(`alert_${alert.id}`));
  }

  /**
   * 获取所有数据库连接列表
   */
  static async getDbConnections(): Promise<DBConnection[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return mockDbConnectionData;
  }

  /**
   * 根据ID获取单个数据库连接
   */
  static async getDbConnectionByIdNumber(id: number): Promise<DBConnection | null> {
    await new Promise(resolve => setTimeout(resolve, 200));
    // 模拟根据ID查询
    const connection = mockDbConnectionData.find(conn => conn.id === id);
    return connection || null;
  }

  /**
   * 根据ID获取数据库连接
   */
  static async getDbConnectionById(id: string): Promise<DBConnection | null> {
    await new Promise(resolve => setTimeout(resolve, 200));
    // 模拟根据ID查询
    const connection = mockDbConnectionData.find(conn => `db_${conn.id}` === id);
    return connection || null;
  }

  /**
   * 获取告警发送列表
   */
  static async getAlertSends(): Promise<AlertSend[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return mockAlertSendData;
  }

  /**
   * 根据ID数组获取告警发送列表
   */
  static async getAlertSendsByIds(ids: string[]): Promise<AlertSend[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    // 模拟根据ID查询
    return mockAlertSendData.filter(send => ids.includes(`send_${send.id}`));
  }

  /**
   * 获取其他信息列表
   */
  static async getOtherInfos(): Promise<OtherInfo[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return mockOtherInfoData;
  }

  /**
   * 根据ID获取其他信息
   */
  static async getOtherInfoById(id: string): Promise<OtherInfo | null> {
    await new Promise(resolve => setTimeout(resolve, 200));
    // 模拟根据ID查询
    const info = mockOtherInfoData.find(info => `info_${info.id}` === id);
    return info || null;
  }

  /**
   * 保存复合表单数据
   */
  static async saveComplexForm(data: TaskBasicFormDataAdd): Promise<TaskBasicFormDataAdd> {
    console.log('保存复合表单数据:', data);
    await new Promise(resolve => setTimeout(resolve, 800));

    const res = await httpClient.post<TaskBasicFormDataAdd>('/api/v1/task/exec', data);

    console.log(res);

    // 由于响应拦截器已经返回了 response.data，所以 res 就是我们需要的数据
    return res;
  }

  /**
   * 更新复合表单数据
   */
  static async updateComplexForm(id: number, data: TaskBasicFormDataUpdateOrDelete): Promise<TaskBasicFormDataUpdateOrDelete> {
    console.log('更新复合表单数据:', id, data);
    await new Promise(resolve => setTimeout(resolve, 800));

    const res = await httpClient.post<TaskBasicFormDataUpdateOrDelete>('/api/v1/task/exec/update', data);

    console.log(res);

    return res;
  }
}
